/home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/path_planning_node
/home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/RB_builder_node
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/ament_index/resource_index/package_run_dependencies/movement_planner_pkg
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/ament_index/resource_index/parent_prefix_path/movement_planner_pkg
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/ament_prefix_path.sh
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/ament_prefix_path.dsv
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/path.sh
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/path.dsv
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.bash
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.sh
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.zsh
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.dsv
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/package.dsv
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/ament_index/resource_index/packages/movement_planner_pkg
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/cmake/movement_planner_pkgConfig.cmake
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/cmake/movement_planner_pkgConfig-version.cmake
/home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/package.xml