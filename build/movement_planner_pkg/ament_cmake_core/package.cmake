set(_AMENT_PACKAGE_NAME "movement_planner_pkg")
set(movement_planner_pkg_VERSION "0.0.0")
set(movement_planner_pkg_MAINTAINER "mac <<EMAIL>>")
set(movement_planner_pkg_BUILD_DEPENDS "rclcpp" "tf2" "tf2_geometry_msgs" "geometry_msgs" "moveit_ros_planning_interface" "moveit_msgs" "shape_msgs" "tf2_ros" "foxglove_msgs")
set(movement_planner_pkg_BUILDTOOL_DEPENDS "ament_cmake")
set(movement_planner_pkg_BUILD_EXPORT_DEPENDS "rclcpp" "tf2" "tf2_geometry_msgs" "geometry_msgs" "moveit_ros_planning_interface" "moveit_msgs" "shape_msgs" "tf2_ros" "foxglove_msgs")
set(movement_planner_pkg_BUILDTOOL_EXPORT_DEPENDS )
set(movement_planner_pkg_EXEC_DEPENDS "rclcpp" "tf2" "tf2_geometry_msgs" "geometry_msgs" "moveit_ros_planning_interface" "moveit_msgs" "shape_msgs" "tf2_ros" "foxglove_msgs")
set(movement_planner_pkg_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(movement_planner_pkg_GROUP_DEPENDS )
set(movement_planner_pkg_MEMBER_OF_GROUPS )
set(movement_planner_pkg_DEPRECATED "")
set(movement_planner_pkg_EXPORT_TAGS)
list(APPEND movement_planner_pkg_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
