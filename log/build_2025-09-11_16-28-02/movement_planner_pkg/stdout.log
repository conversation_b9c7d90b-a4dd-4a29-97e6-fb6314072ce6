-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/local/bin/python3 (found version "3.10.12") found components: Interpreter
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")
-- Found FastRTPS: /opt/ros/humble/include
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0")
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found moveit_ros_planning_interface: 2.5.9 (/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake)
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
-- library: /usr/lib/aarch64-linux-gnu/libcurl.so
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
-- Found foxglove_msgs: 2.3.0 (/opt/ros/humble/share/foxglove_msgs/cmake)
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/Code/ws_0/src/movement_planner_pkg/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done (2.2s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/Code/ws_0/build/movement_planner_pkg
[ 25%] [32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/path_planning_node.dir/src/path_planning_node.cpp.o[0m
[ 75%] [32m[1mLinking CXX executable path_planning_node[0m
[100%] [32m[1mLinking CXX executable RB_builder_node[0m
[100%] Built target path_planning_node
[100%] Built target RB_builder_node
-- Install configuration: ""
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/path_planning_node
-- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/path_planning_node" to ""
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/RB_builder_node
-- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/RB_builder_node" to ""
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/ament_index/resource_index/package_run_dependencies/movement_planner_pkg
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/ament_index/resource_index/parent_prefix_path/movement_planner_pkg
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/path.sh
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/path.dsv
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.bash
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.sh
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.zsh
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.dsv
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/package.dsv
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/ament_index/resource_index/packages/movement_planner_pkg
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/cmake/movement_planner_pkgConfig.cmake
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/cmake/movement_planner_pkgConfig-version.cmake
-- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/package.xml
