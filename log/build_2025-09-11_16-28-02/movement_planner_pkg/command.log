Invoking command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/Code/ws_0/src/movement_planner_pkg -DCMAKE_INSTALL_PREFIX=/home/<USER>/Code/ws_0/install/movement_planner_pkg
Invoked command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/Code/ws_0/src/movement_planner_pkg -DCMAKE_INSTALL_PREFIX=/home/<USER>/Code/ws_0/install/movement_planner_pkg
Invoking command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/movement_planner_pkg -- -j8 -l8
Invoked command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/movement_planner_pkg -- -j8 -l8
Invoking command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/movement_planner_pkg
Invoked command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/movement_planner_pkg
