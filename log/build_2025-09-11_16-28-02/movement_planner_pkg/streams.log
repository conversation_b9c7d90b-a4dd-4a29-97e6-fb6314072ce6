[0.008s] Invoking command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/Code/ws_0/src/movement_planner_pkg -DCMAKE_INSTALL_PREFIX=/home/<USER>/Code/ws_0/install/movement_planner_pkg
[0.178s] -- The C compiler identification is GNU 11.4.0
[0.291s] -- The CXX compiler identification is GNU 11.4.0
[0.303s] -- Detecting C compiler ABI info
[0.346s] -- Detecting C compiler ABI info - done
[0.355s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.355s] -- Detecting C compile features
[0.356s] -- Detecting C compile features - done
[0.365s] -- Detecting CXX compiler AB<PERSON> info
[0.409s] -- Detecting CXX compiler AB<PERSON> info - done
[0.419s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.419s] -- Detecting CXX compile features
[0.420s] -- Detecting CXX compile features - done
[0.447s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.532s] -- Found Python3: /usr/local/bin/python3 (found version "3.10.12") found components: Interpreter
[0.645s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.720s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.733s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.748s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.767s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.795s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.872s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.879s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.101s] -- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "3.0.2")
[1.130s] -- Found FastRTPS: /opt/ros/humble/include
[1.175s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.196s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.234s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.235s] -- Found Threads: TRUE
[1.300s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[1.334s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[1.365s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.367s] -- Found Eigen3: TRUE (found version "3.4.0")
[1.367s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.484s] -- Found moveit_ros_planning_interface: 2.5.9 (/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake)
[1.485s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
[1.485s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.485s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.485s]   set the policy and suppress this warning.
[1.485s] 
[1.485s] Call Stack (most recent call first):
[1.485s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.485s]   CMakeLists.txt:16 (find_package)
[1.485s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.485s] [0m
[1.535s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
[1.677s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[1.677s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.677s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.677s]   set the policy and suppress this warning.
[1.677s] 
[1.678s] Call Stack (most recent call first):
[1.678s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.678s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.678s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.678s]   CMakeLists.txt:16 (find_package)
[1.678s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.678s] [0m
[1.706s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.773s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.798s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.828s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.828s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.828s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.828s]   set the policy and suppress this warning.
[1.828s] 
[1.828s] Call Stack (most recent call first):
[1.828s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.828s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.828s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.828s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.828s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.828s]   CMakeLists.txt:16 (find_package)
[1.828s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.828s] [0m
[1.831s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.908s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[2.037s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[2.037s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.037s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.037s]   set the policy and suppress this warning.
[2.037s] 
[2.037s] Call Stack (most recent call first):
[2.037s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[2.037s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.037s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[2.037s]   CMakeLists.txt:16 (find_package)
[2.037s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.037s] [0m
[2.041s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[2.069s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
[2.069s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.069s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.069s]   set the policy and suppress this warning.
[2.069s] 
[2.069s] Call Stack (most recent call first):
[2.069s]   /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
[2.070s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.070s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[2.070s]   CMakeLists.txt:16 (find_package)
[2.070s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.070s] [0m
[2.073s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[2.096s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
[2.096s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[2.096s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[2.096s]   set the policy and suppress this warning.
[2.096s] 
[2.096s] Call Stack (most recent call first):
[2.096s]   /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
[2.096s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[2.096s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[2.096s]   CMakeLists.txt:16 (find_package)
[2.096s] This warning is for project developers.  Use -Wno-dev to suppress it.
[2.096s] [0m
[2.100s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
[2.137s] -- Found foxglove_msgs: 2.3.0 (/opt/ros/humble/share/foxglove_msgs/cmake)
[2.178s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.253s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[2.254s] -- Configured cppcheck include dirs: /home/<USER>/Code/ws_0/src/movement_planner_pkg/include
[2.254s] -- Configured cppcheck exclude dirs and/or files: 
[2.255s] -- Added test 'lint_cmake' to check CMake code style
[2.256s] -- Added test 'uncrustify' to check C / C++ code style
[2.256s] -- Configured uncrustify additional arguments: 
[2.257s] -- Added test 'xmllint' to check XML markup files
[2.259s] -- Configuring done (2.2s)
[2.307s] -- Generating done (0.0s)
[2.313s] -- Build files have been written to: /home/<USER>/Code/ws_0/build/movement_planner_pkg
[2.323s] Invoked command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake /home/<USER>/Code/ws_0/src/movement_planner_pkg -DCMAKE_INSTALL_PREFIX=/home/<USER>/Code/ws_0/install/movement_planner_pkg
[2.323s] Invoking command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/movement_planner_pkg -- -j8 -l8
[2.341s] [ 25%] [32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o[0m
[2.341s] [ 50%] [32mBuilding CXX object CMakeFiles/path_planning_node.dir/src/path_planning_node.cpp.o[0m
[10.975s] [ 75%] [32m[1mLinking CXX executable path_planning_node[0m
[11.468s] [100%] [32m[1mLinking CXX executable RB_builder_node[0m
[11.776s] [100%] Built target path_planning_node
[11.957s] [100%] Built target RB_builder_node
[11.965s] Invoked command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/movement_planner_pkg -- -j8 -l8
[11.969s] Invoking command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/movement_planner_pkg
[11.971s] -- Install configuration: ""
[11.971s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/path_planning_node
[11.974s] -- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/path_planning_node" to ""
[11.974s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/RB_builder_node
[11.977s] -- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/movement_planner_pkg/lib/movement_planner_pkg/RB_builder_node" to ""
[11.977s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/ament_index/resource_index/package_run_dependencies/movement_planner_pkg
[11.977s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/ament_index/resource_index/parent_prefix_path/movement_planner_pkg
[11.978s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/ament_prefix_path.sh
[11.978s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/ament_prefix_path.dsv
[11.978s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/path.sh
[11.978s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/environment/path.dsv
[11.978s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.bash
[11.979s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.sh
[11.979s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.zsh
[11.980s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/local_setup.dsv
[11.980s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/package.dsv
[11.980s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/ament_index/resource_index/packages/movement_planner_pkg
[11.980s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/cmake/movement_planner_pkgConfig.cmake
[11.980s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/cmake/movement_planner_pkgConfig-version.cmake
[11.981s] -- Installing: /home/<USER>/Code/ws_0/install/movement_planner_pkg/share/movement_planner_pkg/package.xml
[11.983s] Invoked command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/movement_planner_pkg
