[0.000000] (-) TimerEvent: {}
[0.000739] (-) JobUnselected: {'identifier': 'engineer_vision_pkg'}
[0.000772] (-) JobUnselected: {'identifier': 'mindvision_camera'}
[0.000782] (-) JobUnselected: {'identifier': 'rm_vision'}
[0.000790] (-) JobUnselected: {'identifier': 'robot_data_pkg'}
[0.000801] (-) JobUnselected: {'identifier': 'robot_description'}
[0.000809] (-) JobUnselected: {'identifier': 'serial_pkg'}
[0.000825] (movement_planner_pkg) JobQueued: {'identifier': 'movement_planner_pkg', 'dependencies': OrderedDict()}
[0.000837] (movement_planner_pkg) JobStarted: {'identifier': 'movement_planner_pkg'}
[0.011961] (movement_planner_pkg) JobProgress: {'identifier': 'movement_planner_pkg', 'progress': 'cmake'}
[0.017737] (movement_planner_pkg) JobProgress: {'identifier': 'movement_planner_pkg', 'progress': 'build'}
[0.018265] (movement_planner_pkg) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/Code/ws_0/build/movement_planner_pkg', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/Code/ws_0/build/movement_planner_pkg', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-6f17636121051a53c88d3e605c491d22af2ba755/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_visual_tools/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/lib:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface/lib:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager/lib:/home/<USER>/moveit2_ws/install/moveit_setup_assistant/lib:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_controllers/lib:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_framework/lib:/home/<USER>/moveit2_ws/install/moveit_servo/lib:/home/<USER>/moveit2_ws/install/moveit_ros_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction/lib:/home/<USER>/moveit2_ws/install/moveit_ros_perception/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/home/<USER>/moveit2_ws/install/moveit_planners_ompl/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/moveit2_ws/install/moveit_planners_chomp/lib:/home/<USER>/moveit2_ws/install/moveit_kinematics/lib:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/moveit2_ws/install/chomp_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/home/<USER>/moveit2_ws/install/srdfdom/lib:/home/<USER>/moveit2_ws/install/rviz_marker_tools/lib:/home/<USER>/moveit2_ws/install/rosparam_shortcuts/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-6f17636121051a53c88d3e605c491d22af2ba755/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/Code/ws_0/src'), ('TERM_PROGRAM_VERSION', '1.103.2'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-bbc32dcb-b447-4299-ada6-f97b2d5996f4.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-6f17636121051a53c88d3e605c491d22af2ba755/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-6f17636121051a53c88d3e605c491d22af2ba755/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/moveit2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/moveit2_ws/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('PYTHON_EXECUTABLE', '/usr/bin/python3.10'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-ec2f90e73ca2d53c.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-4bcffef955.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-806312771'), ('AMENT_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('CMAKE_ARGS', '-DPYTHON_EXECUTABLE=/usr/bin/python3.10 -DPython3_EXECUTABLE=/usr/bin/python3.10'), ('CMAKE_MODULE_PATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/share/moveit_task_constructor_core/cmake'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('PWD', '/home/<USER>/Code/ws_0/build/movement_planner_pkg'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/moveit2_ws/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('Python3_EXECUTABLE', '/usr/bin/python3.10'), ('CMAKE_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.100382] (-) TimerEvent: {}
[0.129331] (movement_planner_pkg) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.201643] (-) TimerEvent: {}
[0.303790] (-) TimerEvent: {}
[0.309975] (movement_planner_pkg) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.361296] (movement_planner_pkg) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.365508] (movement_planner_pkg) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.373027] (movement_planner_pkg) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.385511] (movement_planner_pkg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.400768] (movement_planner_pkg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.404555] (-) TimerEvent: {}
[0.445103] (movement_planner_pkg) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.450573] (movement_planner_pkg) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.505324] (-) TimerEvent: {}
[0.609432] (-) TimerEvent: {}
[0.715006] (-) TimerEvent: {}
[0.719735] (movement_planner_pkg) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.773413] (movement_planner_pkg) StdoutLine: {'line': b'-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)\n'}
[0.791277] (movement_planner_pkg) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[0.797399] (movement_planner_pkg) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[0.799500] (movement_planner_pkg) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[0.818922] (-) TimerEvent: {}
[0.879022] (movement_planner_pkg) StdoutLine: {'line': b'-- Found moveit_ros_planning_interface: 2.5.9 (/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake)\n'}
[0.879534] (movement_planner_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):\n'}
[0.879808] (movement_planner_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[0.879876] (movement_planner_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[0.879906] (movement_planner_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[0.879933] (movement_planner_pkg) StderrLine: {'line': b'\n'}
[0.879960] (movement_planner_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[0.879986] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[0.880012] (movement_planner_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[0.880051] (movement_planner_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[0.880078] (movement_planner_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[0.892155] (movement_planner_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread\n'}
[0.924029] (-) TimerEvent: {}
[1.009346] (movement_planner_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.009466] (movement_planner_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.009500] (movement_planner_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.009527] (movement_planner_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.009553] (movement_planner_pkg) StderrLine: {'line': b'\n'}
[1.009578] (movement_planner_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.009604] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)\n'}
[1.009630] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.009656] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.009681] (movement_planner_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.009706] (movement_planner_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.009732] (movement_planner_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.017430] (movement_planner_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread\n'}
[1.024169] (-) TimerEvent: {}
[1.050003] (movement_planner_pkg) StdoutLine: {'line': b'-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)\n'}
[1.057531] (movement_planner_pkg) StdoutLine: {'line': b'-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)\n'}
[1.075572] (movement_planner_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):\n'}
[1.075642] (movement_planner_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.075673] (movement_planner_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.075700] (movement_planner_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.075726] (movement_planner_pkg) StderrLine: {'line': b'\n'}
[1.075751] (movement_planner_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.075776] (movement_planner_pkg) StderrLine: {'line': b'  /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)\n'}
[1.075801] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.075841] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)\n'}
[1.075867] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.075899] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.075927] (movement_planner_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.075952] (movement_planner_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.075978] (movement_planner_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.078811] (movement_planner_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem\n'}
[1.115496] (movement_planner_pkg) StdoutLine: {'line': b'-- library: /usr/lib/aarch64-linux-gnu/libcurl.so\n'}
[1.124823] (-) TimerEvent: {}
[1.193163] (movement_planner_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.193280] (movement_planner_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.193313] (movement_planner_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.193341] (movement_planner_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.193368] (movement_planner_pkg) StderrLine: {'line': b'\n'}
[1.193395] (movement_planner_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.193421] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)\n'}
[1.193448] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.193475] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.193500] (movement_planner_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.193527] (movement_planner_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.193607] (movement_planner_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.197119] (movement_planner_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono\n'}
[1.221204] (movement_planner_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.221293] (movement_planner_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.221326] (movement_planner_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.221354] (movement_planner_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.221380] (movement_planner_pkg) StderrLine: {'line': b'\n'}
[1.221407] (movement_planner_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.221433] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)\n'}
[1.221464] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.221493] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.221522] (movement_planner_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.221549] (movement_planner_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.221574] (movement_planner_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.225308] (movement_planner_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options\n'}
[1.225362] (-) TimerEvent: {}
[1.240752] (movement_planner_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.240940] (movement_planner_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.240998] (movement_planner_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.241029] (movement_planner_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.241055] (movement_planner_pkg) StderrLine: {'line': b'\n'}
[1.241083] (movement_planner_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.241108] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)\n'}
[1.241134] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.241161] (movement_planner_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.241186] (movement_planner_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.241210] (movement_planner_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.241235] (movement_planner_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.244452] (movement_planner_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread\n'}
[1.271056] (movement_planner_pkg) StdoutLine: {'line': b'-- Found foxglove_msgs: 2.3.0 (/opt/ros/humble/share/foxglove_msgs/cmake)\n'}
[1.311227] (movement_planner_pkg) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.326718] (-) TimerEvent: {}
[1.359292] (movement_planner_pkg) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[1.359568] (movement_planner_pkg) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/Code/ws_0/src/movement_planner_pkg/include\n'}
[1.359604] (movement_planner_pkg) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[1.360267] (movement_planner_pkg) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.360988] (movement_planner_pkg) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[1.361146] (movement_planner_pkg) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[1.361446] (movement_planner_pkg) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.362382] (movement_planner_pkg) StdoutLine: {'line': b'-- Configuring done (1.3s)\n'}
[1.420797] (movement_planner_pkg) StdoutLine: {'line': b'-- Generating done (0.1s)\n'}
[1.426929] (-) TimerEvent: {}
[1.427328] (movement_planner_pkg) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/Code/ws_0/build/movement_planner_pkg\n'}
[1.448560] (movement_planner_pkg) StdoutLine: {'line': b'[ 16%] \x1b[32mBuilding CXX object CMakeFiles/mtc_node.dir/src/mtc_node.cpp.o\x1b[0m\n'}
[1.474805] (movement_planner_pkg) StdoutLine: {'line': b'[ 50%] Built target RB_builder_node\n'}
[1.474925] (movement_planner_pkg) StdoutLine: {'line': b'[ 83%] Built target path_planning_node\n'}
[1.527977] (-) TimerEvent: {}
[1.536838] (movement_planner_pkg) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/Code/ws_0/src/movement_planner_pkg/src/mtc_node.cpp:1:10:\x1b[m\x1b[K \x1b[01;31m\x1b[Kfatal error: \x1b[m\x1b[Krclcpp/rclcpp.hpp: No such file or directory\n'}
[1.536963] (movement_planner_pkg) StderrLine: {'line': b'    1 | #include \x1b[01;31m\x1b[K"rclcpp/rclcpp.hpp"\x1b[m\x1b[K\n'}
[1.537010] (movement_planner_pkg) StderrLine: {'line': b'      |          \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.537040] (movement_planner_pkg) StderrLine: {'line': b'compilation terminated.\n'}
[1.537696] (movement_planner_pkg) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/mtc_node.dir/build.make:79: CMakeFiles/mtc_node.dir/src/mtc_node.cpp.o] Error 1\n'}
[1.537769] (movement_planner_pkg) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:223: CMakeFiles/mtc_node.dir/all] Error 2\n'}
[1.537831] (movement_planner_pkg) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[1.539878] (movement_planner_pkg) CommandEnded: {'returncode': 2}
[1.548624] (movement_planner_pkg) JobEnded: {'identifier': 'movement_planner_pkg', 'rc': 2}
[1.549303] (-) EventReactorShutdown: {}
