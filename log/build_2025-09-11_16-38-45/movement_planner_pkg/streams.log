[0.018s] Invoking command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/movement_planner_pkg -- -j8 -l8
[0.129s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.309s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.361s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.365s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.372s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.385s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.400s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.444s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.450s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.719s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.773s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[0.790s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[0.797s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.799s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.878s] -- Found moveit_ros_planning_interface: 2.5.9 (/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake)
[0.879s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
[0.879s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.879s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.879s]   set the policy and suppress this warning.
[0.879s] 
[0.879s] Call Stack (most recent call first):
[0.879s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[0.879s]   CMakeLists.txt:16 (find_package)
[0.879s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.879s] [0m
[0.891s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
[1.009s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[1.009s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.009s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.009s]   set the policy and suppress this warning.
[1.009s] 
[1.009s] Call Stack (most recent call first):
[1.009s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.009s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.009s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.009s]   CMakeLists.txt:16 (find_package)
[1.009s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.009s] [0m
[1.017s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.049s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.057s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.075s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.075s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.075s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.075s]   set the policy and suppress this warning.
[1.075s] 
[1.075s] Call Stack (most recent call first):
[1.075s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.075s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.075s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.075s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.075s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.075s]   CMakeLists.txt:16 (find_package)
[1.075s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.075s] [0m
[1.078s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.115s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.192s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[1.192s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.192s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.192s]   set the policy and suppress this warning.
[1.192s] 
[1.192s] Call Stack (most recent call first):
[1.193s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[1.193s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.193s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.193s]   CMakeLists.txt:16 (find_package)
[1.193s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.193s] [0m
[1.196s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[1.220s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
[1.220s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.220s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.220s]   set the policy and suppress this warning.
[1.220s] 
[1.220s] Call Stack (most recent call first):
[1.221s]   /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
[1.221s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.221s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.221s]   CMakeLists.txt:16 (find_package)
[1.221s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.221s] [0m
[1.224s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[1.240s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
[1.240s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.240s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.240s]   set the policy and suppress this warning.
[1.240s] 
[1.240s] Call Stack (most recent call first):
[1.240s]   /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
[1.240s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.240s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.240s]   CMakeLists.txt:16 (find_package)
[1.240s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.240s] [0m
[1.244s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
[1.270s] -- Found foxglove_msgs: 2.3.0 (/opt/ros/humble/share/foxglove_msgs/cmake)
[1.310s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.358s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.359s] -- Configured cppcheck include dirs: /home/<USER>/Code/ws_0/src/movement_planner_pkg/include
[1.359s] -- Configured cppcheck exclude dirs and/or files: 
[1.359s] -- Added test 'lint_cmake' to check CMake code style
[1.360s] -- Added test 'uncrustify' to check C / C++ code style
[1.360s] -- Configured uncrustify additional arguments: 
[1.361s] -- Added test 'xmllint' to check XML markup files
[1.361s] -- Configuring done (1.3s)
[1.420s] -- Generating done (0.1s)
[1.426s] -- Build files have been written to: /home/<USER>/Code/ws_0/build/movement_planner_pkg
[1.448s] [ 16%] [32mBuilding CXX object CMakeFiles/mtc_node.dir/src/mtc_node.cpp.o[0m
[1.474s] [ 50%] Built target RB_builder_node
[1.474s] [ 83%] Built target path_planning_node
[1.536s] [01m[K/home/<USER>/Code/ws_0/src/movement_planner_pkg/src/mtc_node.cpp:1:10:[m[K [01;31m[Kfatal error: [m[Krclcpp/rclcpp.hpp: No such file or directory
[1.536s]     1 | #include [01;31m[K"rclcpp/rclcpp.hpp"[m[K
[1.536s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~[m[K
[1.536s] compilation terminated.
[1.537s] gmake[2]: *** [CMakeFiles/mtc_node.dir/build.make:79: CMakeFiles/mtc_node.dir/src/mtc_node.cpp.o] Error 1
[1.537s] gmake[1]: *** [CMakeFiles/Makefile2:223: CMakeFiles/mtc_node.dir/all] Error 2
[1.537s] gmake: *** [Makefile:146: all] Error 2
[1.539s] Invoked command in '/home/<USER>/Code/ws_0/build/movement_planner_pkg' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/movement_planner_pkg -- -j8 -l8
